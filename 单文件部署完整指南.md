# BIS推送配置 - 单文件部署完整指南

## 当前状态

✅ **已完成的配置：**
1. 安装了Costura.Fody 6.0.0
2. 创建了FodyWeavers.xml配置文件
3. 修复了项目引用路径
4. 复制了必要的依赖文件到项目根目录

## 立即执行步骤

### 步骤1：清理和重新编译

1. **清理项目**
   - 在Visual Studio中：生成 → 清理解决方案
   - 或手动删除bin和obj文件夹

2. **切换到Release模式**
   - 在Visual Studio工具栏中将"Debug"切换为"Release"

3. **重新生成解决方案**
   - 生成 → 重新生成解决方案

### 步骤2：验证编译结果

编译完成后，检查`bin\Release`目录应该包含：

**✅ 应该存在的文件：**
```
BIS推送配置.exe          (主程序，AntdUI.dll已嵌入)
BIS推送配置.exe.config   (配置文件)
IrisSkin4.dll           (皮肤引擎，独立文件)
RealOne.ssk             (皮肤文件)
```

**❌ 不应该存在的文件：**
```
AntdUI.dll              (应该已嵌入到exe中)
AntdUI.xml              (文档文件，不需要)
```

### 步骤3：准备部署包

1. **创建部署文件夹**
   ```
   创建新文件夹：BIS推送配置_部署包
   ```

2. **复制必要文件**
   从`bin\Release`复制以下文件到部署文件夹：
   ```
   BIS推送配置.exe
   BIS推送配置.exe.config
   IrisSkin4.dll
   RealOne.ssk
   ```

### 步骤4：配置目标环境

1. **修改配置文件**
   编辑`BIS推送配置.exe.config`，修改数据库连接字符串：
   ```xml
   <connectionStrings>
       <add name="TEDatabase" 
            connectionString="Server=目标服务器地址;Database=TE;User Id=sa;Password=******;TrustServerCertificate=true;" 
            providerName="System.Data.SqlClient" />
   </connectionStrings>
   ```

2. **确保目标电脑环境**
   - 安装.NET Framework 4.5.2或更高版本
   - 确保可以访问数据库服务器

## 故障排除

### 如果Costura.Fody没有工作

**症状：** bin\Release中仍然有AntdUI.dll文件

**解决方案：**
1. 确认FodyWeavers.xml文件在项目根目录
2. 完全清理项目（删除bin、obj文件夹）
3. 重新生成解决方案
4. 检查编译输出窗口是否有Fody相关信息

### 如果程序无法启动

**可能原因和解决方案：**

1. **缺少.NET Framework**
   - 在目标电脑安装.NET Framework 4.5.2+

2. **缺少依赖文件**
   - 确保IrisSkin4.dll在程序目录中
   - 确保RealOne.ssk皮肤文件存在

3. **数据库连接问题**
   - 检查配置文件中的连接字符串
   - 确保数据库服务器可访问
   - 检查防火墙设置

4. **权限问题**
   - 以管理员身份运行程序
   - 检查程序目录的读写权限

### 如果皮肤引擎出错

**症状：** 程序运行但界面异常

**解决方案：**
1. 确保IrisSkin4.dll版本正确
2. 确保RealOne.ssk文件完整
3. 程序已包含皮肤引擎错误处理，会自动禁用皮肤继续运行

## 验证清单

### 编译验证
- [ ] Release模式编译成功
- [ ] bin\Release中没有AntdUI.dll（已嵌入）
- [ ] bin\Release中有IrisSkin4.dll和RealOne.ssk
- [ ] exe文件大小明显增加（包含嵌入的程序集）

### 部署验证
- [ ] 部署包包含4个必要文件
- [ ] 配置文件中的数据库连接字符串已修改
- [ ] 在干净的测试环境中程序可以启动
- [ ] 所有功能正常工作

### 功能验证
- [ ] 程序界面正常显示
- [ ] 数据库连接成功
- [ ] 增删改查功能正常
- [ ] 皮肤引擎工作正常（或优雅降级）

## 高级配置选项

### 自定义Costura配置

如果需要更精细的控制，可以修改FodyWeavers.xml：

```xml
<Costura>
    <!-- 排除特定程序集 -->
    <ExcludeAssemblies>
        IrisSkin4
    </ExcludeAssemblies>
    
    <!-- 包含特定程序集 -->
    <IncludeAssemblies>
        AntdUI
    </IncludeAssemblies>
    
    <!-- 预加载顺序 -->
    <PreloadOrder>
        AntdUI
    </PreloadOrder>
    
    <!-- 创建临时程序集 -->
    <CreateTemporaryAssemblies>false</CreateTemporaryAssemblies>
    
    <!-- 禁用压缩 -->
    <DisableCompression>false</DisableCompression>
</Costura>
```

## 总结

通过正确配置Costura.Fody，您的应用程序现在可以：
1. 将AntdUI.dll嵌入到主exe文件中
2. 减少部署文件数量（从6个减少到4个）
3. 保持所有功能正常工作
4. 优雅处理皮肤引擎相关问题

**最终部署包只需要4个文件：**
- BIS推送配置.exe（主程序+嵌入的AntdUI）
- BIS推送配置.exe.config（配置文件）
- IrisSkin4.dll（皮肤引擎）
- RealOne.ssk（皮肤文件）

这样就实现了相对简洁的单文件部署方案！
